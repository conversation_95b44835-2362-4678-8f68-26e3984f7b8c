/* eslint-disable no-unused-vars */
import React, {useEffect, useState} from 'react';
import {hydrate, render} from 'react-dom';
import {registerPageInit} from '@/pages/liveshow/service/register';
import bmls from '@baidu/bdmedialive-scheme';
import AIHelper from '@/components/AIHelper';

import '../liveshow/assets/index.less';
import './index.less';
import 'antd/es/button/style';

// 注册所有初始化逻辑
registerPageInit();

// 安全区域适配工具函数
const initSafeAreaAdaptation = () => {
    // 检测是否为Android设备
    const isAndroid = /Android/i.test(navigator.userAgent);

    if (isAndroid) {
        // 尝试通过JSBridge获取状态栏高度
        try {
            // 如果客户端支持获取状态栏高度的方法
            if (window.bmls && typeof window.bmls.getStatusBarHeight === 'function') {
                window.bmls.getStatusBarHeight().then(height => {
                    if (height && height > 0) {
                        document.body.style.paddingTop = `${height}px`;
                    }
                }).catch(() => {
                    // JSBridge调用失败，使用CSS中的经验值
                    console.log('Failed to get status bar height from native, using CSS fallback');
                });
            }
        }
        catch (error) {
            console.log('JSBridge not available, using CSS fallback for Android adaptation');
        }
    }

    // 设置全屏模式（如果需要）
    try {
        // 执行全屏模式设置
        if (window.location.href.indexOf('fullscreen=1') > -1) {
            const fullScreenUrl = 'bdapi://updatetitle?backBtnColor=black&hidetitle=1&hideprogress=1';
            window.location.href = fullScreenUrl;
        }
    }
    catch (error) {
        console.log('Full screen mode setting failed:', error);
    }
};

function App() {
    const [theme, setTheme] = useState('dark'); // 默认日间模式

    useEffect(() => {
        // 初始化安全区域适配
        initSafeAreaAdaptation();

        // 获取主题并设置
        const initTheme = async () => {
            try {
                const themeResult = await bmls.common.getTheme();
                console.log('获取到的主题信息:', themeResult);

                let currentTheme = 'light'; // 默认日间模式

                if (themeResult && themeResult.data) {
                    const {theme: themeType, isNightMode} = themeResult.data;

                    if (themeType === 'dark') {
                        currentTheme = 'dark'; // 暗黑模式
                    } else if (isNightMode) {
                        currentTheme = 'night'; // 夜间模式
                    }
                }

                setTheme(currentTheme);

                // 在html元素上添加主题类名
                const html = document.querySelector('html');
                html.classList.remove('theme-light', 'theme-dark', 'theme-night');
                html.classList.add(`theme-${currentTheme}`);

            } catch (error) {
                console.log('获取主题失败，使用默认日间模式:', error);
                setTheme('light');
                const html = document.querySelector('html');
                html.classList.remove('theme-light', 'theme-dark', 'theme-night');
                html.classList.add('theme-light');
            }
        };

        // initTheme();
    }, []);

    const nativeOptions = {theme};
    return <div className='qingkuang-app-container'>
        <AIHelper host="native" nativeOptions={nativeOptions} />
    </div>;
}

const containerId = 'qingkuang-root';
const rootElement = window.document.getElementById(containerId);
if (rootElement) {
    if (rootElement.hasChildNodes()) {
        hydrate(<App />, rootElement);
    }
    else {
        render(<App />, rootElement);
    }
}

export default locals => {
    return Promise.resolve(
        locals.preRender({
            renderer: () => '',
            id: containerId,
            main: App,
            props: {}
        })
    );
};
