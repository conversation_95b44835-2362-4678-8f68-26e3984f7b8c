import {
    ad_third_url_params,
    ad_url_params,
    queryAntiCheats,
    urlParams
} from '@/pages/liveshow/components/AdLiveShow/AnyDoor/api';
import {request} from '@/pages/liveshow/components/AdLiveShow/AnyDoor/request';
import {getCuid, requestUa} from '@/pages/liveshow/components/AdLiveShow/AnyDoor/utils';


export async function getRoomEnterInfo({uid, roomId}) {
    const {
        bd_vid = '',
        livesource = '',
        source = ''
    } = urlParams || {};
    const cuid = getCuid();
    const jsonParams = Object.fromEntries(new URLSearchParams(window.location.search).entries());
    const params = {
        execTypes: 'queryAnyMountData,queryDynamicPeopleData,queryRoomVideos',
        roomId: +roomId,
        uid,
        cuid,
        ua: requestUa,
        timestamp: Date.now(),
        liveSource: livesource,
        ext: JSON.stringify({
            ext: {
                source: source,
                bd_vid
            }
        }),
        h5RoomUrlParams: JSON.stringify(jsonParams),
        extLog: JSON.stringify({
            bd_vid,
            ad_url_params,
            ad_third_url_params
        })
    };
    try {
        const res = await request({
            path: 'cluelivec/commonApi/v1.0/bizData/get',
            params
        });
        return res;
    }
    catch (error) {
        console.log(error);
        return {};
    }
}

export async function requestRoomEnterInfoWithAntiCheats(roomId, uid) {
    // 创建超时控制器和结果标记
    let isTimeout = false;
    const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
            isTimeout = true;
            reject(new Error('timeout'));
        }, 300);
    });

    try {
        // 竞速请求：反作弊检查 vs 超时控制
        const res = await Promise.race([
            queryAntiCheats({uid, roomId}),
            timeoutPromise
        ]);

        // 正常返回且未超时的情况
        if (res?.antiCheatsResult?.hitAnti) {
            return {};
        }
        return await getRoomEnterInfo({uid, roomId}); // 未命中继续请求
    }
    catch (error) {
        // 处理超时或其它错误
        if (isTimeout) {
            // 超时后发起正常请求
            return await getRoomEnterInfo({uid, roomId});
        }
        throw error; // 其它异常原样抛出
    }
}