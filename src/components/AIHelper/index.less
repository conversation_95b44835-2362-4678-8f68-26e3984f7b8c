/*
 * @file AIHelper less file
 * <AUTHOR> Assistant
 * @date 2025-01-25
 */
@import (reference) './style-util.less';

// 主题颜色变量定义
:root {
    // 日间模式颜色
    --bg-color-light: #fff;
    --text-color-light: #1e1f24;
    --text-secondary-light: #f35;
    --text-tertiary-light: #50525c;
    --text-quaternary-light: #5f6f9;
    --text-hint-light: #848691;
    --text-disabled-light: #eee;
    --text-button-light: #fff;
    --divider-light: #1e1f24;
    --surface-light: #fff;
    --surface-secondary-light: #666;

    // 暗黑模式颜色
    --bg-color-dark: #fff;
    --text-color-dark: #fff;
    --text-secondary-dark: #f35;
    --text-tertiary-dark: #fff;
    --text-quaternary-dark: #292929;
    --text-hint-dark: #fff;
    --text-disabled-dark: #666;
    --text-button-dark: #fff;
    --divider-dark: #fff;
    --surface-dark: #222;
    --surface-secondary-dark: #666;

    // 夜间模式颜色
    --bg-color-night: #666;
    --text-color-night: #666;
    --text-secondary-night: #80192a;
    --text-tertiary-night: #555;
    --text-quaternary-night: #121212;
    --text-hint-night: #555;
    --text-disabled-night: #303030;
    --text-button-night: #fff;
    --divider-night: #666;
    --surface-night: #191919;
    --surface-secondary-night: #303030;
}

// 单位换算：1px=0.01rem
.ai-helper-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    // 默认日间模式
    background: var(--bg-color-light);

    // 固定的tab栏
    .tabs-container {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 100;
        background: var(--surface-light);

        .tabs-wrapper {
            padding: .15rem .17rem 0 .17rem;

            .main-title {
                font-size: .16rem;
                font-weight: 500;
                color: var(--text-color-light);
                text-align: center;
                margin-bottom: .15rem;
                line-height: .25rem;
            }

            .tabs-scroll-container {
                overflow-x: auto;
                overflow-y: hidden;
                -webkit-overflow-scrolling: touch;
                scrollbar-width: none; /* Firefox */
                -ms-overflow-style: none; /* IE and Edge */

                &::-webkit-scrollbar {
                    display: none; /* Chrome, Safari and Opera */
                }

                .tabs {
                    display: flex;
                    gap: .08rem;
                    min-width: max-content;
                    padding-bottom: .02rem; // 防止滚动条遮挡

                    .tab-item {
                        min-width: max-content;
                        width: 1.4rem;
                        padding: .1rem .14rem;
                        font-size: .14rem;
                        text-align: center;
                        border-radius: 1rem;
                        background: #f5f6facc;
                        white-space: nowrap;
                        font-weight: 500;
                        color: var(--text-color-light);

                        &.active {
                            color: var(--text-secondary-light);
                            background: #ff335512;
                        }
                    }
                }
            }
        }
    }

    // 滚动内容区域
    .content-container {
        flex: 1;
        overflow-y: auto;
        padding-top: 1rem; // 为固定tab栏留出空间
        padding-bottom: calc(.18rem + .69rem); /* 为底部卡片留出空间
        background: var(--bg-color-light);=本身底部距离+底部卡片高度 */
        -webkit-overflow-scrolling: touch;

        .content-section::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: .16rem;   /* 和 content-section padding 一样的距离 */
            right: .16rem;  /* 和 content-section padding 一样的距离 */
            height: .01rem;
            background-color: #0000000F;
        }

        .content-section:last-child::after {
            content: none;
        }

        .content-section {
            position: relative;
            padding: .2rem .16rem;

            .section-content {
                .content-text {
                    font-size: .16rem;
                    color: var(--text-color-light);
                    margin-bottom: .16rem;
                    text-align: justify;
                    font-weight: 400;
                }

                .video-container {
                    margin-bottom: .2rem;

                    .video-wrapper {
                        position: relative;
                        width: 1.86rem;
                        border-radius: .08rem;
                        overflow: hidden;
                        cursor: pointer;
                        box-shadow: 0 .02rem .08rem rgba(0, 0, 0, .1);
                    }
                }

                // 猜你想问组件
                .questions-section {
                    margin-top: .2rem;

                    .question-prompt {
                        font-size: .14rem;
                        color: var(--text-hint-light);
                        margin-bottom: .1rem;
                        line-height: .14rem;
                    }

                    .questions-list {
                        display: flex;
                        flex-direction: column;
                        gap: .08rem;

                        .question-item {
                            width: fit-content;
                            max-width: 100%; /* 防止内容超出容器 */
                            padding: .12rem;
                            color: var(--text-color-light);
                            background: #f5f6fa;
                            border-radius: .12rem;
                            border: none;
                            font-size: .14rem;
                            line-height: .14rem;
                            font-weight: 500;
                        }
                    }
                }
            }
        }
    }

    // 底部吸底卡片
    .bottom-card {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 99;
        background: var(--surface-light);
        border-top: .01rem solid #e5e5e5;
        padding: .06rem .17rem;
        box-shadow: 0 -.02rem .08rem rgba(0, 0, 0, .1);
        height: .69rem;

        .card-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: .12rem;
            width: 100%;
            height: 100%;

            .card-left {
                display: flex;
                align-items: center;

                .doctor-icon {
                    width: .48rem;
                    height: .48rem;
                    border-radius: .09rem;
                    object-fit: cover;
                    flex-shrink: 0;
                }

                .card-info {
                    flex: 1;
                    min-width: 0;
                    margin-left: .08rem;

                    .card-title {
                        font-size: .14rem;
                        font-weight: 500;
                        color: var(--text-color-light);
                        line-height: .22rem;
                        margin-bottom: .04rem;
                        .text-ellipse(1);
                    }

                    .card-subtitle {
                        font-size: .12rem;
                        color: var(--text-secondary-light);
                        line-height: .17rem;
                        .text-ellipse(1);
                    }
                }
            }

            .consult-button {
                flex-shrink: 0;
                padding: .1rem .24rem;
                background: linear-gradient(180deg, #FF1F66 0%, #FF4D4D 100%);
                color: #fff;
                font-size: .14rem;
                font-weight: 500;
                border: none;
                border-radius: 1rem;
                cursor: pointer;
                transition: all .3s ease;
                line-height: .2rem;
                min-width: .88rem;
                text-align: center;

                &:hover {
                    background: linear-gradient(135deg, #f33 0%, #ff0052 100%);
                    transform: translateY(-.01rem);
                    box-shadow: 0 .04rem .12rem rgba(255, 77, 77, .3);
                }

                &:active {
                    transform: translateY(0);
                    box-shadow: 0 .02rem .06rem rgba(255, 77, 77, .2);
                }
            }
        }
    }
}

// 全局主题样式
html.theme-dark .ai-helper-container {
    background: var(--bg-color-dark);

    .tabs-container {
        background: var(--surface-dark);

        .main-title {
            color: var(--text-color-dark);
        }

        .tab-item {
            background: #292929cc;
            color: var(--text-color-dark);

            &.active {
                color: var(--text-secondary-dark);
                background: #ff335512;
            }
        }
    }

    .content-container {
        background: var(--bg-color-dark);

        .content-text {
            color: var(--text-color-dark);
        }

        .question-prompt {
            color: var(--text-hint-dark);
        }

        .question-item {
            color: var(--text-color-dark);
            background: #292929;
        }
    }

    .bottom-card {
        background: var(--surface-dark);
        border-top-color: #666;

        .card-title {
            color: var(--text-color-dark);
        }

        .card-subtitle {
            color: var(--text-secondary-dark);
        }
    }
}

html.theme-night .ai-helper-container {
    background: var(--bg-color-night);

    .tabs-container {
        background: var(--surface-night);

        .main-title {
            color: var(--text-color-night);
        }

        .tab-item {
            background: #121212cc;
            color: var(--text-color-night);

            &.active {
                color: var(--text-secondary-night);
                background: #80192a12;
            }
        }
    }

    .content-container {
        background: var(--bg-color-night);

        .content-text {
            color: var(--text-color-night);
        }

        .question-prompt {
            color: var(--text-hint-night);
        }

        .question-item {
            color: var(--text-color-night);
            background: #121212;
        }
    }

    .bottom-card {
        background: var(--surface-night);
        border-top-color: #666;

        .card-title {
            color: var(--text-color-night);
        }

        .card-subtitle {
            color: var(--text-secondary-night);
        }
    }
}
