/*
 * @file AIHelper less file
 * <AUTHOR> Assistant
 * @date 2025-01-25
 */
@import (reference) './style-util.less';

// 单位换算：1px=0.01rem
.ai-helper-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: #fff;

    // 固定的tab栏
    .tabs-container {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 100;
        background: #fff;

        .tabs-wrapper {
            padding: .15rem .17rem 0 .17rem;

            .main-title {
                font-size: .16rem;
                font-weight: 500;
                color: #000;
                text-align: center;
                margin-bottom: .15rem;
                line-height: .25rem;
            }

            .tabs-scroll-container {
                overflow-x: auto;
                overflow-y: hidden;
                -webkit-overflow-scrolling: touch;
                scrollbar-width: none; /* Firefox */
                -ms-overflow-style: none; /* IE and Edge */

                &::-webkit-scrollbar {
                    display: none; /* Chrome, Safari and Opera */
                }

                .tabs {
                    display: flex;
                    gap: .08rem;
                    min-width: max-content;
                    padding-bottom: .02rem; // 防止滚动条遮挡

                    .tab-item {
                        min-width: max-content;
                        width: 1.4rem;
                        padding: .1rem .14rem;
                        font-size: .14rem;
                        text-align: center;
                        border-radius: 1rem;
                        background: #F5F6FACC;
                        white-space: nowrap;
                        font-weight: 500;

                        &.active {
                            color: #F35;
                            background: #FF335512;
                        }
                    }
                }
            }
        }
    }

    // 滚动内容区域
    .content-container {
        flex: 1;
        overflow-y: auto;
        padding-top: 1rem; // 为固定tab栏留出空间
        padding-bottom: calc(.18rem + .69rem); /* 为底部卡片留出空间=本身底部距离+底部卡片高度 */
        -webkit-overflow-scrolling: touch;

        .content-section::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: .16rem;   /* 和 content-section padding 一样的距离 */
            right: .16rem;  /* 和 content-section padding 一样的距离 */
            height: .01rem;
            background-color: #0000000F;
        }

        .content-section:last-child::after {
            content: none;
        }

        .content-section {
            position: relative;
            padding: .2rem .16rem;

            .section-content {
                .content-text {
                    font-size: .16rem;
                    color: #000311;
                    margin-bottom: .16rem;
                    text-align: justify;
                    font-weight: 400;
                }

                .video-container {
                    margin-bottom: .2rem;

                    .video-wrapper {
                        position: relative;
                        width: 1.86rem;
                        border-radius: .08rem;
                        overflow: hidden;
                        cursor: pointer;
                        box-shadow: 0 .02rem .08rem rgba(0, 0, 0, .1);
                    }
                }

                // 猜你想问组件
                .questions-section {
                    margin-top: .2rem;

                    .question-prompt {
                        font-size: .14rem;
                        color: #666;
                        margin-bottom: .1rem;
                        line-height: .14rem;
                    }

                    .questions-list {
                        display: flex;
                        flex-direction: column;
                        gap: .08rem;

                        .question-item {
                            width: fit-content;
                            max-width: 100%; /* 防止内容超出容器 */
                            padding: .12rem;
                            color: #272933;
                            background: #F5F6FA;
                            border-radius: .12rem;
                            border: none;
                            font-size: .14rem;
                            line-height: .14rem;
                            font-weight: 500;
                        }
                    }
                }
            }
        }
    }

    // 底部吸底卡片
    .bottom-card {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 99;
        background: #fff;
        border-top: .01rem solid #e5e5e5;
        padding: .06rem .17rem;
        box-shadow: 0 -.02rem .08rem rgba(0, 0, 0, .1);
        height: .69rem;

        .card-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: .12rem;
            width: 100%;
            height: 100%;

            .card-left {
                display: flex;
                align-items: center;

                .doctor-icon {
                    width: .48rem;
                    height: .48rem;
                    border-radius: .09rem;
                    object-fit: cover;
                    flex-shrink: 0;
                }

                .card-info {
                    flex: 1;
                    min-width: 0;
                    margin-left: .08rem;

                    .card-title {
                        font-size: .14rem;
                        font-weight: 500;
                        color: #1F1F1F;
                        line-height: .22rem;
                        margin-bottom: .04rem;
                        .text-ellipse(1);
                    }

                    .card-subtitle {
                        font-size: .12rem;
                        color: #F35;
                        line-height: .17rem;
                        .text-ellipse(1);
                    }
                }
            }

            .consult-button {
                flex-shrink: 0;
                padding: .1rem .24rem;
                background: linear-gradient(180deg, #FF1F66 0%, #FF4D4D 100%);
                color: #fff;
                font-size: .14rem;
                font-weight: 500;
                border: none;
                border-radius: 1rem;
                cursor: pointer;
                transition: all .3s ease;
                line-height: .2rem;
                min-width: .88rem;
                text-align: center;

                &:hover {
                    background: linear-gradient(135deg, #f33 0%, #ff0052 100%);
                    transform: translateY(-.01rem);
                    box-shadow: 0 .04rem .12rem rgba(255, 77, 77, .3);
                }

                &:active {
                    transform: translateY(0);
                    box-shadow: 0 .02rem .06rem rgba(255, 77, 77, .2);
                }
            }
        }
    }
}
