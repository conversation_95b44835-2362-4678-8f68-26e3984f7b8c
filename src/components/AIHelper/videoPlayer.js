/*
 * @file 视频播放器
 * <AUTHOR>
 * @date 2025-07-28 16:54:13
 */

import React, {useEffect, useRef} from 'react';
import HPlayer from '@baidu/hplayer';
import {aiHelperNativeUtils} from './utils';

export default function VideoPlayer({index, section, isH5}) {
    const {videoUrl, videoCover, id} = section;
    const player = useRef();
    function onClick() {
        if (!isH5) {
            player.current.pause();
            aiHelperNativeUtils.openVideoPlayer(videoUrl, videoCover, id);
        }
    }
    useEffect(() => {
        player.current = new HPlayer({
            container: document.getElementById(`h-video-${index}`),
            video: {
                url: videoUrl,
                pic: videoCover
            },
            fullscreen: 'browser',
            showFullScreen: isH5
        });
        isH5 && player.current.play();
    }, [index, isH5, videoCover, videoUrl]);
    return (
        <div
            onClick={onClick}
            id={`h-video-${index}`}
            style={{width: '100%', height: '100%'}}
        ></div>
    );
}
